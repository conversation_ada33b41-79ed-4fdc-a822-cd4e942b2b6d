package com.scb.atmi.util;

import com.scb.atmi.model.request.WithdrawalReversalRequest;
import com.scb.atmi.model.request.WithdrawalTransactionRequest;

import java.math.BigDecimal;

public class COOPMsgGenerator {
    public static String generateCOOPMsg(WithdrawalTransactionRequest req) {

        StringBuilder sb = new StringBuilder();

        // 1-4: MSG-TYPE PIC X(4)
        sb.append(COOPMsgFormatter.padRight("RQST", 4)); // Usually comes from context, "RQST"
        // 5-20: CARD-NUM PIC X(16)
        sb.append(COOPMsgFormatter.padRight(req.getCardNumber(), 16));
        // 21-22: TRAN-CDE PIC X(2)
        sb.append(COOPMsgFormatter.padRight("10", 2)); // Withdraw = "10", Transfer = "40", fix "40" from mapping
        // 23-24: TRAN-CDE-FROM-TYP PIC X(2)
        sb.append(COOPMsgFormatter.padRight(req.getProcessingCode().getFromAccountType(), 2));
        // 25-26: TRAN-CDE-TO-TYP PIC X(2)
        sb.append(COOPMsgFormatter.padRight(req.getProcessingCode().getToAccountType(), 2));
        // 27-34: AMT-1 PIC 9(8)V99
        sb.append(COOPMsgFormatter.formatAmount(req.getAmounts().getTotalAmount(), 8, 2));
        // 35-47: AMT-2 PIC 9(11)V99 (Use BigDecimal and format)
        sb.append(COOPMsgFormatter.formatAmount(BigDecimal.ZERO, 11, 2)); // If you have second amount, replace it
        // 48-60: AMT-3 PIC 9(11)V99 (Use BigDecimal and format)
        sb.append(COOPMsgFormatter.formatAmount(BigDecimal.ZERO, 11, 2)); // If you have third amount, replace it
        // 61-66: TRACE-NO PIC 9(6)
        sb.append(COOPMsgFormatter.padLeftZero("", 6)); // Usually generated by system
        // 67-72: TRAN-DATE PIC 9(6) - Format: YYMMDD
        sb.append(COOPMsgFormatter.padLeftZero(COOPMsgFormatter.toYYMMDD(req.getPostingDate()), 6));
        // 73-78: TRAN-TIME PIC 9(6)
        sb.append(COOPMsgFormatter.padLeftZero(req.getTransactionTime(), 6));
        // 79-84: APPROVE-CODE PIC X(6) (Usually blank on request)
        sb.append(COOPMsgFormatter.padRight("", 6));
        // 85-87: RESPONSE-CODE PIC 9(3) (blank)
        sb.append(COOPMsgFormatter.padLeftZero("", 3));
        // 88-127: RESPONSE-DESC PIC X(40) (blank)
        sb.append(COOPMsgFormatter.padRight("", 40));
        // 128-129: FROM-ACCT-LGTH PIC 9(2)
        sb.append(COOPMsgFormatter.padLeftZero(req.getFromAccountLength(), 2));
        // 130-148: FROM-ACCT-NUM PIC X(19)
        sb.append(COOPMsgFormatter.padRight(req.getFromAccountNumber(), 19));
        // 149-150: TO-ACCT-LGTH PIC 9(2)
        sb.append(COOPMsgFormatter.padLeftZero(req.getToAccountlength(), 2));
        // 151-169: TO-ACCT-NUM PIC X(19)
        sb.append(COOPMsgFormatter.padRight(req.getToAccountNumber(), 19));
        // 170-175: FEE-AMT PIC 9(4)V99
        sb.append(COOPMsgFormatter.formatAmount(req.getAmounts().getFeeAmount(), 4, 2));
        // 176-191: TERMINAL-ID PIC X(16)
        sb.append(COOPMsgFormatter.padRight(req.getTerminalId(), 16));
        // 192-195: TERM-FTID PIC X(4)
        sb.append(COOPMsgFormatter.padRight(req.getBankTerminalOwner(), 4));
        // 196-199: INSTITUTION-ID PIC X(4)
        sb.append(COOPMsgFormatter.padRight("ATM ", 4)); // Could be another field
        // 200-203: CHANNEL-CD PIC X(4)
        sb.append(COOPMsgFormatter.padRight("2509", 4)); // Could be another field
        // 204-300: FILLER PIC X(97)
        sb.append(COOPMsgFormatter.padRight("", 97));

        String msg = sb.toString();
        return "<![CDATA[" + msg + "]]>";
    }

    public static String generateCOOPMsgForWithdrawalReversal(WithdrawalReversalRequest req) {
        StringBuilder sb = new StringBuilder();

        // 1-4: MSG-TYPE PIC X(4)
        sb.append(COOPMsgFormatter.padRight("RVSL", 4)); // "RVSL" for reversal

        // 5-20: CARD-NUM PIC X(16)
        sb.append(COOPMsgFormatter.padRight(req.getCardNumber(), 16));

        // 21-22: TRAN-CDE PIC X(2)
        sb.append(COOPMsgFormatter.padRight("10", 2)); // FIX '40' for transfer per mapping, change to "10" for withdraw

        // 23-24: TRAN-CDE-FROM-TYP PIC X(2)
        sb.append(COOPMsgFormatter.padRight(req.getProcessingCode().getFromAccountType(), 2));

        // 25-26: TRAN-CDE-TO-TYP PIC X(2)
        sb.append(COOPMsgFormatter.padRight(req.getProcessingCode().getToAccountType(), 2));

        // 27-34: AMT-1 PIC 9(8)V99
        sb.append(COOPMsgFormatter.formatAmount(req.getAmounts().getTotalAmount(), 8, 2));

        // 35-47: AMT-2 PIC 9(11)V99
        sb.append(COOPMsgFormatter.formatAmount(BigDecimal.ZERO, 11, 2)); // if have value, use actual

        // 48-60: AMT-3 PIC 9(11)V99
        sb.append(COOPMsgFormatter.formatAmount(BigDecimal.ZERO, 11, 2)); // if have value, use actual

        // 61-66: TRACE-NO PIC 9(6)
        sb.append(COOPMsgFormatter.padLeftZero("", 6)); // Usually generated by system

        // 67-72: TRAN-DATE PIC 9(6) Format: YYMMDD
        sb.append(COOPMsgFormatter.padLeftZero(COOPMsgFormatter.toYYMMDD(req.getPostingDate()), 6));

        // 73-78: TRAN-TIME PIC 9(6)
        sb.append(COOPMsgFormatter.padLeftZero(req.getTransactionTime(), 6));

        // 79-84: APPROVE-CODE PIC X(6) (blank in reversal, usually present in reply)
        sb.append(COOPMsgFormatter.padRight("", 6));

        // 85-87: RESPONSE-CODE PIC 9(3) (blank)
        sb.append(COOPMsgFormatter.padLeftZero("", 3));

        // 88-127: RESPONSE-DESC PIC X(40) (blank)
        sb.append(COOPMsgFormatter.padRight("", 40));

        // 128-129: FROM-ACCT-LGTH PIC 9(2)
        sb.append(COOPMsgFormatter.padLeftZero(req.getFromAccountlength(), 2));

        // 130-148: FROM-ACCT-NUM PIC X(19)
        sb.append(COOPMsgFormatter.padRight(req.getFromAccountNumber(), 19));

        // 149-150: TO-ACCT-LGTH PIC 9(2)
        sb.append(COOPMsgFormatter.padLeftZero(req.getToAccountlength(), 2));

        // 151-169: TO-ACCT-NUM PIC X(19)
        sb.append(COOPMsgFormatter.padRight(req.getToAccountNumber(), 19));

        // 170-175: FEE-AMT PIC 9(4)V99
        sb.append(COOPMsgFormatter.formatAmount(req.getAmounts().getFeeAmount(), 4, 2));

        // 176-191: TERMINAL-ID PIC X(16)
        sb.append(COOPMsgFormatter.padRight(req.getTerminalId(), 16));

        // 192-195: TERM-FTID PIC X(4)
        sb.append(COOPMsgFormatter.padRight(req.getBankTerminalOwner(), 4));

        // 196-199: INSTITUTION-ID PIC X(4)
        sb.append(COOPMsgFormatter.padRight("ATM ", 4)); // choose as needed

        // 200-203: CHANNEL-CD PIC X(4)
        sb.append(COOPMsgFormatter.padRight("2509", 4)); // choose as needed

        // 204-300: FILLER PIC X(97)
        sb.append(COOPMsgFormatter.padRight("", 97));

        String msg = sb.toString();
        return "<![CDATA[" + msg + "]]>";
    }
}
