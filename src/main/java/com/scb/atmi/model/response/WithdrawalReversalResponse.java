package com.scb.atmi.model.response;

import com.scb.atmi.constant.AtmResponseCodes;
import com.scb.atmi.model.request.WithdrawalReversalRequest;
import com.scb.atmi.library.model.response.coop.CoopResponse;
import com.scb.atmi.library.model.response.paymentDomain.DebitCreditResponse;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "ATM Withdrawal Transaction Response - Message Type 0210")
public class WithdrawalReversalResponse {
    // ==========================================
    // BODY FIELDS
    // ==========================================

    /**
     * Message type (always "0210" for withdrawal response)
     * Fixed value "0210" - Direct field
     */
    @Builder.Default
    private String messageType = "0210";
    private String responseCode;
    private String responseDescription;
    private String availableBalanceSign;
    private BigDecimal availableBalance;
    private String ledgerBalanceSign;
    private BigDecimal ledgerBalance;

    public static WithdrawalReversalResponse createSuccessResponse(
            WithdrawalReversalRequest request,
            BigDecimal availableBalance,
            BigDecimal ledgerBalance) {

        return WithdrawalReversalResponse.builder()
                .messageType(request.getMessageType())
                .responseCode(AtmResponseCodes.APPROVED)
                .responseDescription(AtmResponseCodes.getDescription(AtmResponseCodes.APPROVED))
                .availableBalanceSign(getBalanceSign(availableBalance))
                .availableBalance(availableBalance)
                .ledgerBalanceSign(getBalanceSign(ledgerBalance))
                .ledgerBalance(ledgerBalance)
                .build();
    }

    public static WithdrawalReversalResponse createSuccessResponseCaseCoop(
            BigDecimal availableBalance,
            BigDecimal ledgerBalance) {

        return WithdrawalReversalResponse.builder()
                .messageType("0210")
                .responseCode(AtmResponseCodes.APPROVED)
                .responseDescription(AtmResponseCodes.getDescription(AtmResponseCodes.APPROVED))
                .availableBalanceSign(getBalanceSign(availableBalance))
                .availableBalance(availableBalance)
                .ledgerBalanceSign(getBalanceSign(ledgerBalance))
                .ledgerBalance(ledgerBalance)
                .build();
    }

    public static WithdrawalReversalResponse createSuccessResponseCasePymd(
            BigDecimal availableBalance,
            BigDecimal ledgerBalance) {

        return WithdrawalReversalResponse.builder()
                .messageType("0210")
                .responseCode(AtmResponseCodes.APPROVED)
                .responseDescription(AtmResponseCodes.getDescription(AtmResponseCodes.APPROVED))
                .availableBalanceSign(getBalanceSign(availableBalance))
                .availableBalance(availableBalance)
                .ledgerBalanceSign(getBalanceSign(ledgerBalance))
                .ledgerBalance(ledgerBalance)
                .build();
    }

    public static WithdrawalReversalResponse createErrorResponse(
            String responseCode,
            String responseDescription) {

        return WithdrawalReversalResponse.builder()
                .messageType("0210")
                .responseCode(responseCode)
                .responseDescription(responseDescription)
                .build();
    }

    private static String getBalanceSign(BigDecimal amount) {
        return amount != null ? amount.compareTo(BigDecimal.ZERO) >= 0 ? "+" : "-" : null;
    }
}
