package com.scb.atmi.model.response;

import com.scb.atmi.constant.AtmResponseCodes;
import com.scb.atmi.model.request.WithdrawalTransactionRequest;
import com.scb.atmi.library.model.response.coop.CoopResponse;
import com.scb.atmi.library.model.response.paymentDomain.DebitCreditResponse;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * Withdrawal Transaction Response Model
 * 
 * Response model for ATM withdrawal transactions following
 * the specified field mapping for message type 0210.
 * 
 * Contains only the essential response fields:
 * - correlationId: Echo from request (Header)
 * - messageType: Fixed "0210" (Body, Direct)
 * - responseCode: 00, 56, 58, 96 (Body, Logic)
 * - responseDescription: Response description (Body, Logic)
 * - availableBalanceSign: Balance sign (Body, Logic)
 * - availableBalance: Available balance (Body, Direct)
 * - ledgerBalanceSign: Ledger balance sign (Body, Logic)
 * - ledgerBalance: Ledger balance (Body, Direct)
 * 
 * <AUTHOR> ATM Migration Team
 * @since 2025-07-23
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class WithdrawalTransactionResponse {
    @Builder.Default
    private String messageType = "0210";
    private String responseCode;
    private String responseDescription;
    private String availableBalanceSign;
    private BigDecimal availableBalance;
    private String ledgerBalanceSign;
    private BigDecimal ledgerBalance;

    public static WithdrawalTransactionResponse createSuccessResponseCaseCoop(
        BigDecimal availableBalance,
        BigDecimal ledgerBalance) {

        return WithdrawalTransactionResponse.builder()
            .messageType("0210")
            .responseCode(AtmResponseCodes.APPROVED)
            .responseDescription(AtmResponseCodes.getDescription(AtmResponseCodes.APPROVED))
            .availableBalanceSign(availableBalance != null && availableBalance.compareTo(BigDecimal.ZERO) >= 0 ? "+" : "-")
            .availableBalance(availableBalance)
            .ledgerBalanceSign(ledgerBalance != null && ledgerBalance.compareTo(BigDecimal.ZERO) >= 0 ? "+" : "-")
            .ledgerBalance(ledgerBalance)
            .build();
    }

    public static WithdrawalTransactionResponse createSuccessResponseCasePymd(
        BigDecimal availableBalance,
        BigDecimal ledgerBalance) {

        return WithdrawalTransactionResponse.builder()
            .messageType("0210")
            .responseCode(AtmResponseCodes.APPROVED)
            .responseDescription(AtmResponseCodes.getDescription(AtmResponseCodes.APPROVED))
            .availableBalanceSign(getBalanceSign(availableBalance))
            .availableBalance(availableBalance)
            .ledgerBalanceSign(getBalanceSign(ledgerBalance))
            .ledgerBalance(ledgerBalance)
            .build();
    }

    private static String getBalanceSign(BigDecimal amount) {
        return amount != null ? amount.compareTo(BigDecimal.ZERO) >= 0 ? "+" : "-" : null;
    }

    public static WithdrawalTransactionResponse createErrorResponse(
            String responseCode,
            String responseDescription) {

        return WithdrawalTransactionResponse.builder()
            .messageType("0210")
            .responseCode(responseCode)
            .responseDescription(responseDescription)
            .build();
    }
}
