package com.scb.atmi.model.request;

import com.scb.atmi.enums.AccountType;
import com.scb.atmi.enums.MachineType;
import com.scb.atmi.enums.ProductType;
import com.scb.atmi.enums.TerminalType;
import com.scb.atmi.enums.ToAccountType;
import com.scb.atmi.library.validator.EnumValidator;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import jakarta.validation.Valid;
import jakarta.validation.constraints.*;
import java.math.BigDecimal;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "ATM Withdrawal Transaction Request - Message Type 0200")
public class WithdrawalTransactionRequest {
    // ==========================================
    // BODY FIELDS
    // ==========================================

    /**
     * Product Code
     * Length: 2, Mandatory
     * Values: "00" = ATM, "04" = POS
     */
    @NotBlank
    @Size(max = 2)
    @EnumValidator(enumClass = ProductType.class)
    @Schema(description = "Product code", example = "00")
    private String product;

    /**
     * Message Type
     * Length: 4, Mandatory
     * Value: "0200" for withdrawal request
     */
    @NotBlank
    @Pattern(regexp = "^0200$")
    @Schema(description = "Message type", example = "0200")
    private String messageType;

    /**
     * Original Message Type
     * Length: 4, Mandatory
     * Value: "0000" for request
     */
    @NotBlank
    @Pattern(regexp = "^0000$")
    @Schema(description = "Original message type", example = "0000")
    private String originalMessageType;

    /**
     * Processing Code
     * Mandatory, Data Block
     */
    @NotNull
    @Valid
    @Schema(description = "Processing code information")
    private ProcessingCode processingCode;

    /**
     * Processing Code Data Block
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ProcessingCode {
        /**
         * Transaction Type
         * Length: 2, Mandatory
         * Value: "10" for withdrawal
         */
        @NotBlank
        @Pattern(regexp = "^10$")
        @Schema(description = "Transaction type", example = "10")
        private String transactionType;

        /**
         * From Account Type
         * Length: 2, Mandatory
         * Values: "11" = Savings, "01" = Current, "13" = Fixed
         */
        @NotBlank
        @Size(max = 2)
        @EnumValidator(enumClass = AccountType.class)
        @Schema(description = "From account type", example = "11")
        private String fromAccountType;

        /**
         * To Account Type
         * Length: 2, Mandatory
         * Values: "00" = Normal withdrawal, "01" = Fast cash
         */
        @NotBlank
        @Size(max = 2)
        @EnumValidator(enumClass = ToAccountType.class)
        @Schema(description = "To account type", example = "00")
        private String toAccountType;
    }

    // ==========================================
    // AMOUNT FIELDS
    // ==========================================

    /**
     * Amounts Information
     * Mandatory, Data Block
     */
    @NotNull
    @Valid
    @Schema(description = "Amount information")
    private Amounts amounts;

    /**
     * Amounts Data Block
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Amounts {
        @NotNull
        @Digits(integer = 12, fraction = 2)
        @Schema(description = "Total withdrawal amount", example = "1000.00")
        private BigDecimal totalAmount;

        @Digits(integer = 2, fraction = 2)
        @Schema(description = "Transaction fee amount", example = "15.00")
        private BigDecimal feeAmount;

        @Digits(integer = 2, fraction = 2)
        @Schema(description = "Fee charged to source account", example = "5.00")
        private BigDecimal fromAccountFee;
    }

    // ==========================================
    // CARD FIELDS
    // ==========================================

    /**
     * Card Length
     * Length: 2, Mandatory
     */
    @NotBlank
    @Size(max = 2)
    @Schema(description = "Card length", example = "16")
    private String cardLength;

    /**
     * Card Number (PAN)
     * Length: 19, Mandatory, Numeric
     * Will be masked in logs for PCIDSS compliance
     */
    @NotBlank
    @Size(max = 19)
    @Pattern(regexp = "^\\d+$")
    @Schema(description = "Customer card number", example = "**********123456789")
    private String cardNumber;

    /**
     * Card Reference
     * Length: 16, Optional
     */
    @Size(max = 16)
    @Schema(description = "Card reference", example = "REF**********123")
    private String cardReference;

    /**
     * Card Type
     * Length: 2, Mandatory
     * Card type = 18 and from acct type = 01 → COOP a/c
     */
    @NotBlank
    @Size(max = 2)
    @Schema(description = "Card type", example = "18")
    private String cardType;

    /**
     * Bank Card Owner
     * Length: 4, Mandatory
     * eg. "SCB"
     */
    @NotBlank
    @Size(max = 4)
    @Schema(description = "Bank card owner", example = "SCB")
    private String bankCardOwner;

    /**
     * Service Code
     * Length: 3, Optional, Numeric
     * Service code from magnetic stripe
     */
    @Size(min = 3, max = 3)
    @Pattern(regexp = "^\\d{3}$")
    @Schema(description = "Service code from magnetic stripe", example = "201")
    private String serviceCode;

    // ==========================================
    // ACCOUNT FIELDS
    // ==========================================

    /**
     * From Account Length
     * Length: 2, Mandatory
     */
    @NotBlank
    @Size(max = 2)
    @Schema(description = "From account length", example = "10")
    private String fromAccountLength;

    /**
     * From Account Number
     * Length: 19, Mandatory
     */
    @NotBlank
    @Size(max = 19)
    @Schema(description = "From account number", example = "**********")
    private String fromAccountNumber;

    /**
     * To Account Length
     * Length: 2, Optional
     */
    @Size(max = 2)
    @Schema(description = "To account length", example = "10")
    private String toAccountlength;

    /**
     * To Account Number
     * Length: 19, Optional
     */
    @Size(max = 19)
    @Schema(description = "To account number", example = "**********")
    private String toAccountNumber;

    // ==========================================
    // TERMINAL FIELDS
    // ==========================================

    /**
     * Terminal ID
     * Length: 16, Mandatory, Alphanumeric
     * ATM terminal identifier
     */
    @NotBlank
    @Size(max = 16)
    @Pattern(regexp = "^[A-Za-z0-9]+$")
    @Schema(description = "ATM terminal identifier", example = "ATM00123")
    private String terminalId;

    /**
     * Terminal Sequence
     * Length: 6, Mandatory, Numeric
     * Terminal sequence number for transaction tracking
     */
    @NotBlank
    @Size(min = 6, max = 6)
    @Pattern(regexp = "^\\d{6}$")
    @Schema(description = "Terminal sequence number", example = "000001")
    private String terminalSequence;

    /**
     * Terminal Type
     * Length: 2, Mandatory
     * Values: 00, 01, 03, 08, 11, 20, 22, 30, 31, 37, 50, 52, 55, 60, 70, 80
     */
    @NotBlank
    @Size(max = 2)
    @EnumValidator(enumClass = TerminalType.class)
    @Schema(description = "Terminal type", example = "00")
    private String terminalType;

    @Size(max = 4)
    @Schema(description = "Terminal branch id", example = "0000")
    private String terminalBranchId;

    /**
     * Terminal Location
     * Length: 25, Optional
     */
    @Size(max = 25)
    @Schema(description = "Terminal location", example = "Bangkok Central Plaza")
    private String terminalLocation;

    /**
     * Bank Terminal Owner
     * Length: 4, Mandatory
     * FIID - eg. "SCB", "MDS", "KBNK", "BBLA"
     */
    @NotBlank
    @Size(max = 4)
    @Schema(description = "Bank terminal owner FIID", example = "SCB")
    private String bankTerminalOwner;

    // ==========================================
    // CONFIGURATION FIELDS
    // ==========================================

    /**
     * Currency Code
     * Length: 3, Optional
     * Default: "THB"
     */
    @Size(min = 3, max = 3)
    @Pattern(regexp = "^[A-Z]{3}$")
    @Schema(description = "Currency code", example = "THB")
    private String currencyCode;

    /**
     * Machine Type
     * Length: 1, Optional
     * Values: N = ATM, O = Other, M = MCC
     */
    @Size(min = 1, max = 1)
    @EnumValidator(enumClass = MachineType.class)
    @Schema(description = "ATM machine type indicator", example = "N")
    private String machineType;

    // ==========================================
    // DATE/TIME FIELDS
    // ==========================================

    /**
     * Posting Date
     * Length: 6, Mandatory
     * Format: "MMDDYY"
     */
    @NotBlank
    @Size(min = 6, max = 6)
    @Pattern(regexp = "^\\d{6}$")
    @Schema(description = "Posting date", example = "122523")
    private String postingDate;

    /**
     * Transaction Date
     * Length: 4, Mandatory
     * Format: "MMDD"
     */
    @NotBlank
    @Size(min = 4, max = 4)
    @Pattern(regexp = "^\\d{4}$")
    @Schema(description = "Transaction date", example = "1225")
    private String transactionDate;

    /**
     * Transaction Time
     * Length: 6, Mandatory
     * Format: "HHMMSS"
     */
    @NotBlank
    @Size(min = 6, max = 6)
    @Pattern(regexp = "^\\d{6}$")
    @Schema(description = "Transaction time", example = "143052")
    private String transactionTime;
}
