package com.scb.atmi.model;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

@Data
@NoArgsConstructor
@AllArgsConstructor
@SuperBuilder
public class RespError {
    private String messageType;
    private String responseCode;
    private String responseDescription;
    private String availableBalanceSign;
    private Double availableBalance;
    private String ledgerBalanceSign;
    private Double ledgerBalance;

    public static RespError createErrorResponse(
        String responseCode,
        String responseDescription) {

        return RespError.builder()
            .messageType("0210")
            .responseCode(responseCode)
            .responseDescription(responseDescription)
            .build();
    }
}
