package com.scb.atmi.constant;

public final class AtmResponseCodes {

    private AtmResponseCodes() {
    }

    public static final String APPROVED = "00";
    public static final String FILE_NOT_OPEN = "54";
    public static final String INVALID_ACCOUNT = "56";
    public static final String EXTERNAL_ERROR = "71";
    public static final String AIS_REJECT = "72";

    public static final String MSG_DUPLICATE_TRANSACTION = "Duplicate transaction";

    public static String getDescription(String responseCode) {
        return switch (responseCode) {
            case APPROVED -> "Success";
            case FILE_NOT_OPEN -> "Database not open";
            case INVALID_ACCOUNT -> "Invalid account";
            case AIS_REJECT -> "Destination not available";
            case EXTERNAL_ERROR -> "Transaction not allow";
            default -> "UNKNOWN ERROR";
        };
    }
}
