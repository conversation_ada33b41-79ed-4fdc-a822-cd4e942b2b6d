package com.scb.atmi.service;

import com.scb.atmi.constant.AtmResponseCodes;
import com.scb.atmi.enums.ResponseFrom;
import com.scb.atmi.library.communication.rest.RestServiceException;
import com.scb.atmi.library.dto.DuplicateCheckDto;
import com.scb.atmi.library.entity.TransactionLogEntity;
import com.scb.atmi.library.log.AppLogger;
import com.scb.atmi.library.log.LoggerFactory;
import com.scb.atmi.library.model.CardRefCheckResult;
import com.scb.atmi.library.model.DuplicateCheckResult;
import com.scb.atmi.library.model.constant.HeaderConstant;
import com.scb.atmi.library.model.request.coop.CoopRequest;
import com.scb.atmi.library.model.request.notification.NotiRequest;
import com.scb.atmi.library.model.request.notification.PublicationBody;
import com.scb.atmi.library.model.request.paymentDomain.DebitCreditRequest;
import com.scb.atmi.library.model.request.paymentDomain.debitCredit.AccountRequest;
import com.scb.atmi.library.model.request.paymentDomain.debitCredit.Fees;
import com.scb.atmi.library.model.request.paymentDomain.debitCredit.RemittanceInfo;
import com.scb.atmi.library.model.request.paymentDomain.debitCredit.StatementDescription;
import com.scb.atmi.library.model.response.coop.CoopResponse;
import com.scb.atmi.library.model.response.paymentDomain.DebitCreditResponse;
import com.scb.atmi.library.model.response.paymentDomain.debitCredit.AccountBalance;
import com.scb.atmi.library.repository.TransactionLogRepository;
import com.scb.atmi.library.service.CardProfileService;
import com.scb.atmi.library.service.CoopService;
import com.scb.atmi.library.service.DuplicateCheckService;
import com.scb.atmi.library.service.PaymentDomainService;
import com.scb.atmi.library.service.async.AsyncAutoRetryService;
import com.scb.atmi.library.service.async.AsyncNotificationService;
import com.scb.atmi.library.service.cache.ResponseLookupService;
import com.scb.atmi.library.util.AtmUtil;
import com.scb.atmi.library.util.CardUtils;
import com.scb.atmi.model.request.WithdrawalTransactionRequest;
import com.scb.atmi.model.response.WithdrawalTransactionResponse;
import com.scb.atmi.util.COOPMsgGenerator;
import com.scb.atmi.util.CommonUtil;
import com.scb.atmi.util.DuplicateCheckMapper;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.UUID;

import static com.scb.atmi.constant.CommonConstants.*;
import static com.scb.atmi.library.service.async.AsyncAutoRetryService.COOP;
import static com.scb.atmi.library.service.async.AsyncAutoRetryService.PYMD;

@Service
@RequiredArgsConstructor
public class WithdrawalService {
    private final AppLogger log = LoggerFactory.getAppLogger(this.getClass());
    private final DuplicateCheckService duplicateCheckService;
    private final CardProfileService cardProfileService;
    private final TransactionLogRepository transactionLogRepository;
    private final CoopService coopService;
    private final AsyncAutoRetryService asyncAutoRetryService;
    private final PaymentDomainService paymentDomainService;
    private final AsyncNotificationService asyncNotificationService;
    private final ResponseLookupService responseLookupService;

    public WithdrawalTransactionResponse processWithdrawalConfirm(HttpHeaders headers, WithdrawalTransactionRequest request) {
        log.info("[process-withdrawal-confirm] start process");
        String correlationId = headers.getFirst(HeaderConstant.CORRELATION_ID);

        log.info("[process-withdrawal-confirm] validating transaction request");
        WithdrawalTransactionResponse validationResponse = validateTransaction(request);
        if (validationResponse != null) {
            log.info("[process-withdrawal-confirm] validation failed, returning error response");
            return validationResponse;
        }

        log.info("[process-withdrawal-confirm] initializing transaction");
        initializeTransaction(request);

        log.info("[process-withdrawal-confirm] checking card reference");
        CardRefCheckResult cardRefResult = cardProfileService.checkCardRef(request.getCardReference(), request.getCardNumber());
        if (cardRefResult.getStatus() != CardRefCheckResult.Status.SUCCESS) {
            log.info("[process-withdrawal-confirm] card reference check failed: {}", cardRefResult.getMessage());
            return WithdrawalTransactionResponse.createErrorResponse(
                cardRefResult.getErrorCode(), cardRefResult.getMessage());
        }
        request.setCardNumber(cardRefResult.getCardRef());

        log.info("[process-withdrawal-confirm] checking duplicate transactions");
        // Check for duplicate transactions using new DTO approach
        DuplicateCheckDto duplicateCheckDto = DuplicateCheckMapper.fromWithdrawalRequest(request, false);
        DuplicateCheckResult duplicateCheck =
            duplicateCheckService.checkDuplicateTransaction(duplicateCheckDto);

        if (duplicateCheck.isError()) {
            log.info("[process-withdrawal-confirm] duplicate check error occurred");
            return handleDuplicateCheckError();
        }

        if (duplicateCheck.isDuplicate()) {
            log.info("[process-withdrawal-confirm] duplicate transaction detected");
            return handleDuplicateTransaction();
        }

        log.info("[process-withdrawal-confirm] inserting transaction log");
        TransactionLogEntity respTransLogInsert = this.insertTransactionLog(request, TRANSACTION_STATUS_WITHDRAWAL_IN_PROGRESS);
        WithdrawalTransactionResponse response = null;
        try {
            response = processWithdrawalByCardType(request, correlationId);
            return response;
        } catch (Exception e) {
            log.error("[process-withdrawal-confirm] error occurred: {}", e.getMessage());
            return WithdrawalTransactionResponse.createErrorResponse(AtmResponseCodes.AIS_REJECT, AtmResponseCodes.getDescription(AtmResponseCodes.AIS_REJECT));
        } finally {
            if (response != null && AtmResponseCodes.APPROVED.equals(response.getResponseCode())) {
                respTransLogInsert.setTransactionStatus(TRANSACTION_STATUS_WITHDRAWAL_SUCCESS);
            } else {
                if (AtmResponseCodes.AIS_REJECT.equals(response.getResponseCode())) respTransLogInsert.setTimeoutFlag(true);
                respTransLogInsert.setTransactionStatus(TRANSACTION_STATUS_WITHDRAWAL_FAILED);
            }
            updateTransactionLog(respTransLogInsert);
        }

    }

    private WithdrawalTransactionResponse processWithdrawalByCardType(WithdrawalTransactionRequest request, String correlationId) {
        if (isGiftCard(request.getCardType())) {
            log.info("[process-withdrawal-confirm] processing gift card withdrawal");
            // COBOL: B24SEM-CARD-TYPE = '25' OR '19' (line 1008)
            return returnGiftCardWithdrawal();
        } else if (isCoopCard(request)) {
            log.info("[process-withdrawal-confirm] processing coop card withdrawal");
            return processCoopWithdrawal(request, correlationId);

        } else {
            if (isOwnerTerm(request)) {
                log.info("[process-withdrawal-confirm] owner terminal detected, returning external error");
                return WithdrawalTransactionResponse.createErrorResponse(
                    AtmResponseCodes.EXTERNAL_ERROR, AtmResponseCodes.getDescription(AtmResponseCodes.EXTERNAL_ERROR));
            }
            if (isFastCash(request)) {
                log.info("[process-withdrawal-confirm] processing fast cash withdrawal");
                // call post v4 debit credit
                return processWithdrawalTransactionFastCash(request, correlationId);
            } else {
                log.info("[process-withdrawal-confirm] processing non-fast cash withdrawal");
                return processWithdrawalNonFastCash(request, correlationId);
            }
        }
    }

    private WithdrawalTransactionResponse processCoopWithdrawal(WithdrawalTransactionRequest request, String correlationId) {
        try {
            CoopRequest coopRequest = buildCoopRequest(request);
            CoopResponse coopResponse = coopService.callReverseService(coopRequest, correlationId);
            if ("0000".equals(coopResponse.getDocRs().getMerchantCOOPRs().getStatus().getStatusCode())){
                String atmTransactionId = duplicateCheckService.generateTransactionLogKey(
                    DuplicateCheckMapper.fromWithdrawalRequest(request, false)
                );
                NotiRequest notiRequest = NotiRequest.createNotiRequest("01",
                        atmTransactionId,
                        PublicationBody.builder()
                                .messageType("RQST")
                                .cardMaskedNumber(CardUtils.maskCardNumber(request.getCardNumber())) // Mask card number
                                .transactionAmount(request.getAmounts().getTotalAmount())
                                .transactionFee(request.getAmounts().getFeeAmount())
                                .currencyCode("274")
                                .toAccount(request.getToAccountNumber())
//                                .availableBalanceSign(availableBalance != null && availableBalance.compareTo(BigDecimal.ZERO) >= 0 ? "+" : "-")
//                                .availableBalance(availableBalance != null ? availableBalance.abs() : null)
//                                .ledgerBalanceSign(ledgerBalance != null && ledgerBalance.compareTo(BigDecimal.ZERO) >= 0 ? "+" : "-")
//                                .ledgerBalance(ledgerBalance != null ? ledgerBalance.abs() : null)
                                .build());
                asyncNotificationService.sendNotificationAsync(notiRequest, correlationId);
            }
            return WithdrawalTransactionResponse.createSuccessResponseCaseCoop(BigDecimal.ZERO, BigDecimal.ZERO);
        } catch (RestServiceException e) {
            log.error("[process-withdrawal-coop] error occurred: {}", e);
            String errorCode = AtmUtil.handleClientError(e);
            if ("JMS01".equals(errorCode) || AtmResponseCodes.AIS_REJECT.equals(errorCode)) {
                log.info("[process-withdrawal-coop] JMS01 error detected in 504 response");
                return autoRetryCoop(request, correlationId);
            } else {
                String responseCode = responseLookupService.getResponseCode(
                    request.getProcessingCode().getTransactionType(),
                    ResponseFrom.PYMD.getValue(),
                    errorCode
                );
                return WithdrawalTransactionResponse.createErrorResponse(
                    responseCode,
                    AtmResponseCodes.getDescription(responseCode)
                );
            }
        } catch (Exception e) {
            return autoRetryCoop(request, correlationId);
        }
    }

    private WithdrawalTransactionResponse processWithdrawalNonFastCash(WithdrawalTransactionRequest request, String correlationId) {
        try {
            return processSaving(request, correlationId);
        } catch (RestServiceException e) {
            log.error("[process-withdrawal-non-fast-cash] error occurred: {}", e);
            String errorCode = AtmUtil.handleClientError(e);
            if ("JMS01".equals(errorCode) || AtmResponseCodes.AIS_REJECT.equals(errorCode)) {
                log.info("[process-withdrawal-non-fast-cash] JMS01 error detected in 504 response");
                return autoRetryPymd(request, true, correlationId);
            } else {
                String responseCode = responseLookupService.getResponseCode(
                    request.getProcessingCode().getTransactionType(),
                    ResponseFrom.PYMD.getValue(),
                    errorCode
                );
                return WithdrawalTransactionResponse.createErrorResponse(
                    responseCode,
                    AtmResponseCodes.getDescription(responseCode)
                );
            }
        } catch (Exception e) {
            log.info("[withdrawal-non-fast-cash] exception error: {}", e);
            return WithdrawalTransactionResponse.createErrorResponse(AtmResponseCodes.AIS_REJECT, AtmResponseCodes.getDescription(AtmResponseCodes.AIS_REJECT));
        }
    }

    private WithdrawalTransactionResponse processWithdrawalTransactionFastCash(WithdrawalTransactionRequest request, String correlationId) {
        try {
            return processSaving(request, correlationId);
        } catch (RestServiceException e) {
            log.error("[withdrawal-transaction-fast-cash] error occurred: {}", e);
            String errorCode = AtmUtil.handleClientError(e);
            if ("JMS01".equals(errorCode) || AtmResponseCodes.AIS_REJECT.equals(errorCode)) {
                log.info("[withdrawal-transaction-fast-cash] JMS01 error detected in 504 response");
                autoRetryPymd(request, true, correlationId);
                return processCurrent(request, correlationId);
            } else {
                String responseCode = responseLookupService.getResponseCode(
                    request.getProcessingCode().getTransactionType(),
                    ResponseFrom.PYMD.getValue(),
                    errorCode
                );
                return WithdrawalTransactionResponse.createErrorResponse(
                    responseCode,
                    AtmResponseCodes.getDescription(responseCode)
                );
            }
        } catch (Exception e) {
            autoRetryPymd(request, true, correlationId);
            return processCurrent(request, correlationId);
        }
    }

    private WithdrawalTransactionResponse processCurrent(WithdrawalTransactionRequest request, String correlationId) {
        log.info("[withdrawal-process-current] Processing saving account withdrawal");
        try {
            DebitCreditRequest debitCreditRequest = buildDebitCreditRequest(
                    request,
                    "",
                    request.getAmounts().getTotalAmount(),
                    false,
                    PYMD_TRANSACTION_DEBIT_TYPE
            );
            DebitCreditResponse pymdResponse = paymentDomainService.postDebitCreditServiceInfo(
                debitCreditRequest,
                getOriginalSystem(request),
                correlationId
            );

            log.info("[withdrawal-process-current] pymd response: {}", pymdResponse);
            AccountBalance filterResponseAvailBalance = null;
            AccountBalance filterResponseBalance = null;
            if (checkResponseIsNull(pymdResponse)) {
                filterResponseAvailBalance = filterPymdResponseAvailBalance(pymdResponse);
                filterResponseBalance = filterPymdResponseBalance(pymdResponse);
            }
            NotiRequest notiRequest = buildNotiRequest(request, false, null, pymdResponse);
            asyncNotificationService.sendNotificationAsync(notiRequest, correlationId);
            return WithdrawalTransactionResponse.createSuccessResponseCasePymd(filterResponseAvailBalance.getAmount(), filterResponseBalance.getAmount());
        } catch (RestServiceException e) {
            log.error("[withdrawal-process-current] error occurred: {}", e);
            String errorCode = AtmUtil.handleClientError(e);
            if ("JMS01".equals(errorCode) || AtmResponseCodes.AIS_REJECT.equals(errorCode)) {
                return autoRetryPymd(request, false, correlationId);
            } else {
                String responseCode = responseLookupService.getResponseCode(
                    request.getProcessingCode().getTransactionType(),
                    ResponseFrom.PYMD.getValue(),
                    errorCode
                );
                return WithdrawalTransactionResponse.createErrorResponse(
                    responseCode,
                    AtmResponseCodes.getDescription(responseCode)
                );
            }
        } catch (Exception e) {
            return autoRetryPymd(request, false, correlationId);
        }
    }

    private WithdrawalTransactionResponse processSaving(WithdrawalTransactionRequest request, String correlationId) {
        log.info("[withdrawal-process-saving] Processing saving account withdrawal");

        DebitCreditRequest debitCreditRequest = buildDebitCreditRequest(
                request,
                "",
                request.getAmounts().getTotalAmount(),
                true,
                PYMD_TRANSACTION_DEBIT_TYPE
        );
        DebitCreditResponse pymdResponse = paymentDomainService.postDebitCreditServiceInfo(
            debitCreditRequest,
            getOriginalSystem(request),
            correlationId
        );

        log.info("[withdrawal-process-saving] pymd response: {}", pymdResponse);
        AccountBalance filterResponseAvailBalance = null;
        AccountBalance filterResponseBalance = null;
        if (checkResponseIsNull(pymdResponse)) {
            filterResponseAvailBalance = filterPymdResponseAvailBalance(pymdResponse);
            filterResponseBalance = filterPymdResponseBalance(pymdResponse);
        }
        NotiRequest notiRequest = buildNotiRequest(request, false, null, pymdResponse);
        asyncNotificationService.sendNotificationAsync(notiRequest,correlationId);

        return WithdrawalTransactionResponse.createSuccessResponseCasePymd(filterResponseAvailBalance.getAmount(), filterResponseBalance.getAmount());
    }

    private AccountBalance filterPymdResponseAvailBalance(DebitCreditResponse pymdResponse) {
        return pymdResponse.getFromAccount().getBalanceInfo().getAccountBalances()
            .stream()
            .filter(b -> PYMD_ACCOUNT_BALANCE_TYPE_AVAILBALANCE.equalsIgnoreCase(b.getBalanceType()))
            .findFirst()
            .orElse(new AccountBalance());
    }

    private AccountBalance filterPymdResponseBalance(DebitCreditResponse pymdResponse) {
        return pymdResponse.getFromAccount().getBalanceInfo().getAccountBalances()
            .stream()
            .filter(b -> PYMD_ACCOUNT_BALANCE_TYPE_BALANCE.equalsIgnoreCase(b.getBalanceType()))
            .findFirst()
            .orElse(new AccountBalance());
    }

    private WithdrawalTransactionResponse autoRetryPymd(WithdrawalTransactionRequest request, boolean isSaving, String correlationId) {
        DebitCreditRequest debitCreditRequest = buildDebitCreditRequest(
                request,
                "",
                request.getAmounts().getTotalAmount(),
                isSaving,
                PYMD_TRANSACTION_REVDEBIT_TYPE
        );
        NotiRequest notiRequest = buildNotiRequest(request, true,null, null);
        asyncAutoRetryService.executeAsyncAutoRetryService(debitCreditRequest, null, getOriginalSystem(request),
                correlationId, PYMD, notiRequest);
        return WithdrawalTransactionResponse.createErrorResponse(
            AtmResponseCodes.AIS_REJECT, AtmResponseCodes.getDescription(AtmResponseCodes.AIS_REJECT));
    }

    private WithdrawalTransactionResponse autoRetryCoop(WithdrawalTransactionRequest request, String correlationId) {
        CoopRequest coopRequest = buildCoopRequest(request);
        NotiRequest notiRequest = buildNotiRequest(request, true,null, null);
        asyncAutoRetryService.executeAsyncAutoRetryService(null, coopRequest, getOriginalSystem(request),
                correlationId, COOP, notiRequest);
        return WithdrawalTransactionResponse.createErrorResponse(
            AtmResponseCodes.AIS_REJECT, AtmResponseCodes.getDescription(AtmResponseCodes.AIS_REJECT));
    }

    private BigDecimal getBalance(DebitCreditResponse pymdResponse, String balanceType) {
        return pymdResponse.getFromAccount().getBalanceInfo().getAccountBalances().stream()
                .filter(b -> balanceType.equalsIgnoreCase(b.getBalanceType()))
                .map(b -> b.getAmount())
                .findFirst()
                .orElse(null);
    }

    private NotiRequest buildNotiRequest(WithdrawalTransactionRequest request,
                                         boolean isReversal,
                                         CoopResponse coopResponse,
                                         DebitCreditResponse debitCreditResponse) {
        String transactionLogKey = duplicateCheckService.generateTransactionLogKey(
                DuplicateCheckMapper.fromWithdrawalRequest(request, request.getOriginalMessageType() != null)
        );
        String subEventCode = "01";
        String messageType = "RQST";
        if (isReversal) {
            subEventCode = "02";
            messageType = "RVSL";
        }
        NotiRequest notiRequest = NotiRequest.createNotiRequest(subEventCode,
                transactionLogKey,
                PublicationBody.builder()
                        .messageType(messageType)
                        .cardMaskedNumber(CardUtils.maskCardNumber(request.getCardNumber())) // Mask card number
                        .transactionAmount(request.getAmounts().getTotalAmount())
                        .transactionFee(request.getAmounts().getFeeAmount())
                        .currencyCode("764")
                        .fromAccount(request.getFromAccountNumber())
                        .toAccount(request.getToAccountNumber())
                        .build());
        if (coopResponse != null) {
            // TODO: set coop response to noti request
        } else if (debitCreditResponse != null) {
            BigDecimal availableBalance = null;
            BigDecimal ledgerBalance = null;
            if (checkResponseIsNull(debitCreditResponse)) {
                availableBalance = getBalance(debitCreditResponse, PYMD_ACCOUNT_BALANCE_TYPE_AVAILBALANCE);
                ledgerBalance = getBalance(debitCreditResponse, PYMD_ACCOUNT_BALANCE_TYPE_BALANCE);
            }
            notiRequest.getPublicationBody().setAvailableBalanceSign(CommonUtil.getBalanceSign(availableBalance));
            notiRequest.getPublicationBody().setAvailableBalance(CommonUtil.safeAbs(availableBalance));
            notiRequest.getPublicationBody().setLedgerBalanceSign(CommonUtil.getBalanceSign(ledgerBalance));
            notiRequest.getPublicationBody().setLedgerBalance(CommonUtil.safeAbs(ledgerBalance));
        }
        return notiRequest;
    }

    private boolean checkResponseIsNull(DebitCreditResponse debitCreditResponse) {
        return debitCreditResponse.getFromAccount() != null &&
            debitCreditResponse.getFromAccount().getBalanceInfo() != null &&
            debitCreditResponse.getFromAccount().getBalanceInfo().getAccountBalances() != null;
    }

    private CoopRequest buildCoopRequest(WithdrawalTransactionRequest request){
        String uuid = UUID.randomUUID().toString().replace("-", "");
        String coopMsg = COOPMsgGenerator.generateCOOPMsg(request);
        return CoopRequest.builder().docRq(CoopRequest.DocRq.builder()
                        .merchantCOOPRq(CoopRequest.DocRq.MerchantCOOPRq.builder()
                                .rqUID(uuid)
                                .tranType("KUSD")
                                .coopMsg(coopMsg)
                                .build())
                        .build())
            .build();
    }

    private DebitCreditRequest buildDebitCreditRequest(
        WithdrawalTransactionRequest request,
        String originalRequestUid,
        BigDecimal amount,
        boolean isSaving,
        String transactionType
    ) {
        return DebitCreditRequest.builder()
            .bankCode("14")
            .deviceId(request.getTerminalId())
            .financialType("ETFR")
            .originalRequestUid(originalRequestUid)
            .processingBranch(request.getTerminalBranchId())
            .terminalNumber(request.getTerminalId())
            .transactionType(transactionType)
            .fromAccount(AccountRequest.builder()
                .accountCurrency("764")
                .accountNumber(isSaving ? request.getFromAccountNumber() : request.getToAccountNumber())
                .build())
            .remittanceInfo(RemittanceInfo.builder()
                .amount(amount)
                .feeChargeAcct("DEPACCTIDFROM")
                .fees(Arrays.asList(
                    Fees.builder()
                        .feeAmount(request.getAmounts().getFeeAmount())
                        .feeAmountCurrency("764")
                        .feeType("TRAN")
                        .build()
                ))
                .build())
            .statementDescription(StatementDescription.builder()
                .fromDepInfo("SCB".equals(request.getBankCardOwner()) ? request.getTerminalLocation() : request.getTerminalType().concat(request.getBankTerminalOwner()))
                .build())
            .build();
    }

    private boolean isFastCash(WithdrawalTransactionRequest request) {
        return "101101".equals(
            request.getProcessingCode().getTransactionType()
            .concat(request.getProcessingCode().getFromAccountType())
            .concat(request.getProcessingCode().getToAccountType())
        );
    }

    private boolean isOwnerTerm(WithdrawalTransactionRequest request) {
        return !isScbTerminal(request.getBankCardOwner()) && !isDivisibleBy100(request.getAmounts().getTotalAmount());
    }

    public boolean isDivisibleBy100(BigDecimal number) {
        BigDecimal hundred = new BigDecimal("100");
        return number.remainder(hundred).compareTo(BigDecimal.ZERO) == 0;
    }

    private boolean isScbTerminal(String cardOwner) {
        return "SCB".equals(cardOwner) || "MDS".equals(cardOwner) || "VISA".equals(cardOwner);
    }

    private boolean isCoopCard(WithdrawalTransactionRequest request) {
        return "18".equals(request.getCardType()) && "01".equals(request.getProcessingCode().getFromAccountType());
    }

    public WithdrawalTransactionResponse validateTransaction(WithdrawalTransactionRequest request) {
        String transactionType = request.getProcessingCode().getTransactionType();
        String fromAccountType = request.getProcessingCode().getFromAccountType();

        // Validate terminal ID
        if (!TRANSACTION_TYPE_ALLOWED.equals(transactionType)) {
            return WithdrawalTransactionResponse.createErrorResponse(
                AtmResponseCodes.EXTERNAL_ERROR, AtmResponseCodes.getDescription(AtmResponseCodes.EXTERNAL_ERROR));
        }

        if (FIXE_ACCOUNT_NOT_ALLOWED.equals(fromAccountType)) {
            return WithdrawalTransactionResponse.createErrorResponse(
                AtmResponseCodes.INVALID_ACCOUNT, AtmResponseCodes.getDescription(AtmResponseCodes.INVALID_ACCOUNT));
        }

        if (request.getAmounts().getTotalAmount().compareTo(BigDecimal.ZERO) <= 0) {
            return WithdrawalTransactionResponse.createErrorResponse(
                AtmResponseCodes.EXTERNAL_ERROR, AtmResponseCodes.getDescription(AtmResponseCodes.EXTERNAL_ERROR));
        }
        return null;
    }

    public void initializeTransaction(WithdrawalTransactionRequest request) {
        // Set default values if not provided
        if (request.getTransactionDate() == null) {
            request.setTransactionDate(LocalDate.now().format(DateTimeFormatter.ofPattern("yyyyMMdd")));
        }

        if (request.getTransactionTime() == null) {
            request.setTransactionTime(LocalTime.now().format(DateTimeFormatter.ofPattern("HHmmss")));
        }

        if (request.getPostingDate() == null) {
            request.setPostingDate(request.getTransactionDate());
        }
    }

    private WithdrawalTransactionResponse handleDuplicateTransaction() {
        return WithdrawalTransactionResponse.createErrorResponse(
            AtmResponseCodes.INVALID_ACCOUNT,  AtmResponseCodes.MSG_DUPLICATE_TRANSACTION);
    }

    private WithdrawalTransactionResponse handleDuplicateCheckError() {
        return WithdrawalTransactionResponse.createErrorResponse(
            AtmResponseCodes.FILE_NOT_OPEN,  AtmResponseCodes.getDescription(AtmResponseCodes.FILE_NOT_OPEN));
    }

    private boolean isGiftCard(String accountType) {
        // Gift card account types
        return "25".equals(accountType) || "19".equals(accountType);
    }

    private WithdrawalTransactionResponse returnGiftCardWithdrawal() {
        return WithdrawalTransactionResponse.createErrorResponse(
            AtmResponseCodes.EXTERNAL_ERROR, AtmResponseCodes.getDescription(AtmResponseCodes.EXTERNAL_ERROR) );
    }

    @Transactional
    public TransactionLogEntity insertTransactionLog(WithdrawalTransactionRequest request, String status) {
        String transactionLogKey = duplicateCheckService.generateTransactionLogKey(
            DuplicateCheckMapper.fromWithdrawalRequest(request, false)
        );
        log.info("Inserting transaction log: {}", request);
        TransactionLogEntity transactionLog = TransactionLogEntity.builder()
            .atmTransactionId(transactionLogKey)
            .bankTerminalOwner(request.getBankTerminalOwner())
            .terminalId(request.getTerminalId())
            .sequenceNo(request.getTerminalSequence())
            .cardType(request.getCardType())
            .cardRef(request.getTerminalSequence())
            .cardMaskNo(request.getCardNumber())
            .fromAccount(request.getFromAccountNumber())
            .toAccount(request.getToAccountNumber())
            .messageType(request.getMessageType())
            .originalMessageType(request.getOriginalMessageType())
            .transactionType(request.getProcessingCode().getTransactionType())
            .transactionStatus(status)
            .transactionDate(request.getTransactionDate())
            .transactionTime(request.getTransactionTime())
            .postingDate(request.getPostingDate())
            .processingCode(getProcessingCode(request))
            .transactionAmount(request.getAmounts().getTotalAmount())
            .transactionFee(request.getAmounts().getFeeAmount())
            .currency(request.getCurrencyCode())
            .createdDate(LocalDateTime.now())
            .updatedDate(LocalDateTime.now())
            .build();
        return transactionLogRepository.save(transactionLog);
    }

    @Transactional
    public void updateTransactionLog(TransactionLogEntity transactionLog) {
        log.info("Update transaction log: {}", transactionLog);
        transactionLog.setUpdatedDate(LocalDateTime.now());
        transactionLogRepository.save(transactionLog);
    }


    private String getProcessingCode(WithdrawalTransactionRequest request) {
        return request.getProcessingCode().getTransactionType() + request.getProcessingCode().getFromAccountType() + request.getProcessingCode().getToAccountType();
    }

    private String getOriginalSystem(WithdrawalTransactionRequest request) {
        String bankTerminalOwner = request.getBankTerminalOwner();
        String terminalId = request.getTerminalId();
        if ("SCB".equals(bankTerminalOwner)) {
            if (terminalId != null && terminalId.length() >= 6) {
                char position6 = terminalId.charAt(5);
                if (position6 == '6') {
                    return "SCDM";
                } else {
                    return "SATM";
                }
            } else {
                return "SATM";
            }
        } else if ("MDS".equals(bankTerminalOwner)) {
            return "IATM";
        } else {
            return "OATM";
        }
    }
}
