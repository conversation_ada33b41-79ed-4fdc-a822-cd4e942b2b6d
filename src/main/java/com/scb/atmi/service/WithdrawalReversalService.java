package com.scb.atmi.service;

import com.scb.atmi.constant.AtmResponseCodes;
import com.scb.atmi.enums.ResponseFrom;
import com.scb.atmi.enums.RevesalType;
import com.scb.atmi.library.communication.rest.RestServiceException;
import com.scb.atmi.library.dto.DuplicateCheckDto;
import com.scb.atmi.library.entity.TransactionLogEntity;
import com.scb.atmi.library.log.AppLogger;
import com.scb.atmi.library.log.LoggerFactory;
import com.scb.atmi.library.model.CardRefCheckResult;
import com.scb.atmi.library.model.DuplicateCheckResult;
import com.scb.atmi.library.model.constant.HeaderConstant;
import com.scb.atmi.library.model.constant.ResponseConstants;
import com.scb.atmi.library.model.request.coop.CoopRequest;
import com.scb.atmi.library.model.request.notification.NotiRequest;
import com.scb.atmi.library.model.request.notification.PublicationBody;
import com.scb.atmi.library.model.request.paymentDomain.DebitCreditRequest;
import com.scb.atmi.library.model.request.paymentDomain.debitCredit.AccountRequest;
import com.scb.atmi.library.model.request.paymentDomain.debitCredit.Fees;
import com.scb.atmi.library.model.request.paymentDomain.debitCredit.RemittanceInfo;
import com.scb.atmi.library.model.request.paymentDomain.debitCredit.StatementDescription;
import com.scb.atmi.library.model.response.coop.CoopResponse;
import com.scb.atmi.library.model.response.paymentDomain.DebitCreditResponse;
import com.scb.atmi.library.repository.TransactionLogRepository;
import com.scb.atmi.library.service.CardProfileService;
import com.scb.atmi.library.service.CoopService;
import com.scb.atmi.library.service.DuplicateCheckService;
import com.scb.atmi.library.service.PaymentDomainService;
import com.scb.atmi.library.service.async.AsyncNotificationService;
import com.scb.atmi.library.service.cache.ResponseLookupService;
import com.scb.atmi.library.util.AtmUtil;
import com.scb.atmi.library.util.CardUtils;
import com.scb.atmi.model.request.WithdrawalReversalRequest;
import com.scb.atmi.model.response.WithdrawalReversalResponse;
import com.scb.atmi.util.COOPMsgGenerator;
import com.scb.atmi.util.CommonUtil;
import com.scb.atmi.util.DuplicateCheckMapper;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.UUID;

import static com.scb.atmi.constant.CommonConstants.*;

@Service
@RequiredArgsConstructor
public class WithdrawalReversalService {
    private final AppLogger log = LoggerFactory.getAppLogger(this.getClass());
    private final DuplicateCheckService duplicateCheckService;
    private final CardProfileService cardProfileService;
    private final TransactionLogRepository transactionLogRepository;
    private final CoopService coopService;
    private final AsyncNotificationService asyncNotificationService;
    private final PaymentDomainService paymentDomainService;
    private final ResponseLookupService responseLookupService;

    public WithdrawalReversalResponse processWithdrawalReversal(HttpHeaders headers, WithdrawalReversalRequest request) {
        log.info("[process-withdrawal-reversal] start process");
        String correlationId = headers.getFirst(HeaderConstant.CORRELATION_ID);

        // Validate transaction request
        log.info("[process-withdrawal-reversal] validating transaction request");
        WithdrawalReversalResponse validationResponse = validateReversalTransaction(request);
        if (validationResponse != null) {
            log.info("[process-withdrawal-reversal] validation failed, returning error response");
            return validationResponse;
        }

        // Check card reference
        log.info("[process-withdrawal-reversal] checking card reference");
        CardRefCheckResult cardRefResult = cardProfileService.checkCardRef(request.getCardReference(), request.getCardNumber());
        if (cardRefResult.getStatus() != CardRefCheckResult.Status.SUCCESS) {
            log.info("[process-withdrawal-reversal] card reference check failed: {}", cardRefResult.getMessage());
            return WithdrawalReversalResponse.createErrorResponse(
                    cardRefResult.getErrorCode(), cardRefResult.getMessage());
        } else {
            log.info("[process-withdrawal-reversal] card reference check passed");
            request.setCardReference(cardRefResult.getCardRef());
        }

        // Check for duplicate transactions
        log.info("[process-withdrawal-reversal] checking duplicate reversal transaction");
        DuplicateCheckDto duplicateCheckDto = DuplicateCheckMapper.fromWithdrawalReversalRequest(request, false);
        DuplicateCheckResult duplicateCheck =
                duplicateCheckService.checkDuplicateReversalTransaction(duplicateCheckDto);

        if (ResponseConstants.MESSAGE_TYPE_0400.equals(request.getMessageType())) {
            if (duplicateCheck.isDuplicate()) {
                log.info("[process-withdrawal-reversal] duplicate reversal transaction detected");
                return handleDuplicateTransactionForReversal(); // 56
            }

            if (duplicateCheck.isError()) {
                log.info("[process-withdrawal-reversal] duplicate reversal check error occurred");
                return handleDuplicateCheckErrorForReversal(); // 54
            }
        } else if (
                ResponseConstants.MESSAGE_TYPE_9220.equals(request.getMessageType()) ||
                        ResponseConstants.MESSAGE_TYPE_9221.equals(request.getMessageType())) {

            if (duplicateCheck.isError()) {
                log.info("[process-withdrawal-reversal] duplicate reversal check error occurred");
                return handleDuplicateCheckErrorForReversal();
            }

            if (duplicateCheck.isReturnSuccess()) {
                log.info("[process-withdrawal-reversal] duplicate reversal check returned success, returning success response");
                // Stop and return success response
                return WithdrawalReversalResponse.createSuccessResponse(request, BigDecimal.ZERO, BigDecimal.ZERO);
            }
        } else {
            log.info("[process-withdrawal-reversal] unsupported message type: {}", request.getMessageType());
            return  handle71Error();
        }

        // Check original transaction
        log.info("[process-withdrawal-reversal] checking original transaction for reversal");
        DuplicateCheckDto duplicateOriginalCheckDto = DuplicateCheckMapper.fromWithdrawalReversalRequest(request, true);
        duplicateOriginalCheckDto.setMessageType("0200");
        DuplicateCheckResult duplicateOriginalCheck =
                duplicateCheckService.checkTransactionError(duplicateOriginalCheckDto, TRANSACTION_STATUS_WITHDRAWAL_FAILED);

        if (!duplicateOriginalCheck.isDuplicate()) {
            log.info("[process-withdrawal-reversal] original transaction not found");
            return handle71Error();
        }

        if (duplicateOriginalCheck.isError()) {
            log.info("[process-withdrawal-reversal] original transaction duplicate check error");
            return handleDuplicateCheckErrorForReversal();
        }

        log.info("[process-withdrawal-reversal] inserting reversal transaction log");
        TransactionLogEntity transactionLog = insertReversalTransactionLog(request, TRANSACTION_STATUS_WITHDRAWAL_REVERSE_IN_PROGRESS);

        RevesalType reversalType = null;
        // Validate amount
        log.info("[process-withdrawal-reversal] validating amounts for reversal");
        if (request.getAmounts().getDispensedAmount() == null || request.getAmounts().getDispensedAmount().compareTo(BigDecimal.ZERO) == 0) {
            // Continue with reversal processing
            log.info("[process-withdrawal-reversal] continue with reversal processing (Full reversed)");
            reversalType = RevesalType.FULL;
        } else if (request.getAmounts().getDispensedAmount().compareTo(request.getAmounts().getTotalAmount()) > 0) {
            // Continue with reversal processing
            log.info("[process-withdrawal-reversal] continue with reversal processing (Over dispensed)");
            reversalType = RevesalType.OVER;
        } else if (request.getAmounts().getDispensedAmount().compareTo(request.getAmounts().getTotalAmount()) < 0) {
            // Continue with reversal processing
            log.info("[process-withdrawal-reversal] continue with reversal processing (Patial reversed)");
            reversalType = RevesalType.PARTIAL;
        } else if (request.getAmounts().getDispensedAmount().equals(request.getAmounts().getTotalAmount())) {
            // Return to success
            log.info("[process-withdrawal-reversal] return success to channel");
            return WithdrawalReversalResponse.createSuccessResponse(request, null, null);
        }

        // Withdraw process
        WithdrawalReversalResponse response = null;
        try {
            log.info("[process-withdrawal-reversal] starting reversal processing: {}", reversalType);
            return response = processWithdrawalByCardType(request, correlationId, reversalType);
        } catch (Exception e) {
            log.error("[process-withdrawal-reversal] error during reverse: {}", e.getMessage(), e);
            return WithdrawalReversalResponse.createErrorResponse(AtmResponseCodes.AIS_REJECT, AtmResponseCodes.getDescription(AtmResponseCodes.AIS_REJECT));
        } finally {
            log.info("[process-withdrawal-reversal] updating transaction log");
            if (response != null && AtmResponseCodes.APPROVED.equals(response.getResponseCode())) {
                log.info("[process-withdrawal-reversal] reversal successful, updating status to success");
                transactionLog.setTransactionStatus(TRANSACTION_STATUS_WITHDRAWAL_REVERSE_SUCCESS);
            } else {
                log.info("[process-withdrawal-reversal] reversal failed, updating status to failed");
                if (response != null && AtmResponseCodes.AIS_REJECT.equals(response.getResponseCode())) {
                    transactionLog.setTimeoutFlag(true);
                }
                transactionLog.setTransactionStatus(TRANSACTION_STATUS_WITHDRAWA_REVERSEL_FAILED);
            }
            updateTransactionLog(transactionLog);
            log.info("[process-withdrawal-reversal] end process");
        }
    }

    public WithdrawalReversalResponse validateReversalTransaction(WithdrawalReversalRequest request) {
        String transactionType = request.getProcessingCode().getTransactionType();
        String fromAccountType = request.getProcessingCode().getFromAccountType();

        // Validate terminal ID
        if (!TRANSACTION_TYPE_ALLOWED.equals(transactionType)) {
            return WithdrawalReversalResponse.createErrorResponse(
                    AtmResponseCodes.EXTERNAL_ERROR, AtmResponseCodes.getDescription(AtmResponseCodes.EXTERNAL_ERROR));
        }

        if (FIXE_ACCOUNT_NOT_ALLOWED.equals(fromAccountType)) {
            return WithdrawalReversalResponse.createErrorResponse(
                    AtmResponseCodes.INVALID_ACCOUNT, AtmResponseCodes.getDescription(AtmResponseCodes.INVALID_ACCOUNT));
        }

        if (request.getAmounts().getTotalAmount().compareTo(BigDecimal.ZERO) <= 0) {
            return WithdrawalReversalResponse.createErrorResponse(
                    AtmResponseCodes.EXTERNAL_ERROR, AtmResponseCodes.getDescription(AtmResponseCodes.EXTERNAL_ERROR));
        }
        return null;
    }

    @Transactional
    public TransactionLogEntity insertReversalTransactionLog(WithdrawalReversalRequest request, String status) {
        DuplicateCheckDto duplicateCheckDto = DuplicateCheckMapper.fromWithdrawalReversalRequest(request, true);
        String transactionLogKey = duplicateCheckService.generateTransactionLogKey(duplicateCheckDto);
        log.info("Inserting transaction log: {}", request);
        TransactionLogEntity transactionLog = TransactionLogEntity.builder()
                .atmTransactionId(transactionLogKey)
                .bankTerminalOwner(request.getBankTerminalOwner())
                .terminalId(request.getTerminalId())
                .sequenceNo(request.getTerminalSequence())
                .cardType(request.getCardType())
                .cardRef(request.getTerminalSequence())
                .cardMaskNo(request.getCardNumber())
                .fromAccount(request.getFromAccountNumber())
                .toAccount(request.getToAccountNumber())
                .messageType(request.getMessageType())
                .originalMessageType(request.getOriginalMessageType())
                .transactionType(request.getProcessingCode().getTransactionType())
                .transactionStatus(status)
                .transactionDate(request.getTransactionDate())
                .transactionTime(request.getTransactionTime())
                .postingDate(request.getPostingDate())
                .processingCode(getProcessingCode(request))
                .transactionAmount(request.getAmounts().getTotalAmount())
                .transactionFee(request.getAmounts().getFeeAmount())
                .currency(request.getCurrencyCode())
                .createdDate(LocalDateTime.now())
                .updatedDate(LocalDateTime.now())
                .build();
        return transactionLogRepository.save(transactionLog);
    }

    private String getProcessingCode(WithdrawalReversalRequest request) {
        return request.getProcessingCode().getTransactionType() + request.getProcessingCode().getFromAccountType() + request.getProcessingCode().getToAccountType();
    }

    public void updateTransactionLog(TransactionLogEntity transactionLog) {
        log.info("Update transaction log: {}", transactionLog);

        transactionLogRepository.save(transactionLog);
    }

    private WithdrawalReversalResponse processCoopWithdrawalReversal(WithdrawalReversalRequest request, String correlationId) {
        try {
            log.info("[process-withdrawal-reversal] calling coop reverse service");
            CoopRequest coopRequest = buildCoopRequest(request);
            CoopResponse coopResponse = coopService.callReverseService(coopRequest, correlationId);
            if ("0000".equals(coopResponse.getDocRs().getMerchantCOOPRs().getStatus().getStatusCode())){
                log.info("[process-withdrawal-reversal] coop reverse successful, sending notification");
                NotiRequest notiRequest = buildNotiRequest(request, coopResponse, null);
                asyncNotificationService.sendNotificationAsync(notiRequest, correlationId);
            } else {
                log.info("[process-withdrawal-reversal] coop reverse failed with code: {}", coopResponse.getDocRs().getMerchantCOOPRs().getStatus().getStatusCode());
            }
            return WithdrawalReversalResponse.createSuccessResponseCaseCoop(BigDecimal.ZERO, BigDecimal.ZERO);
        } catch (Exception e) {
            log.error("[process-withdrawal-reversal] error during coop reverse: {}", e.getMessage(), e);
            return WithdrawalReversalResponse.createErrorResponse(AtmResponseCodes.AIS_REJECT, AtmResponseCodes.getDescription(AtmResponseCodes.AIS_REJECT));
        }
    }

    private CoopRequest buildCoopRequest(WithdrawalReversalRequest request){
        String uuid = UUID.randomUUID().toString().replace("-", "");
        String coopMsg = COOPMsgGenerator.generateCOOPMsgForWithdrawalReversal(request);
        return CoopRequest.builder().docRq(CoopRequest.DocRq.builder()
                        .merchantCOOPRq(CoopRequest.DocRq.MerchantCOOPRq.builder()
                                .rqUID(uuid)
                                .tranType("KUSD")
                                .coopMsg(coopMsg)
                                .build())
                        .build())
                .build();
    }

    private boolean isCoopCard(WithdrawalReversalRequest request) {
        return "18".equals(request.getCardType()) && "01".equals(request.getProcessingCode().getFromAccountType());
    }

    private WithdrawalReversalResponse processWithdrawalByCardType(WithdrawalReversalRequest request,
                                                                   String correlationId,
                                                                   RevesalType reversalType) {
        WithdrawalReversalResponse response = null;
        if (isCoopCard(request)) {
            log.info("[process-withdrawal-reversal] processing coop card withdrawal");
            response = processCoopWithdrawalReversal(request, correlationId);
        } else {
            // call post v4 debit credit
            log.info("[process-withdrawal-reversal] processing pymd reversal");
            switch (reversalType) {
                case FULL:
                    log.info("[process-withdrawal-reversal] reversal type is FULL");
                    response = processReversalPymd(request, correlationId, request.getAmounts().getTotalAmount());
                    break;
                case OVER:
                    log.info("[process-withdrawal-reversal] reversal type is OVER");
                    response = processReversalPymd(request, correlationId, request.getAmounts().getDispensedAmount());
                    break;
                case PARTIAL:
                    log.info("[process-withdrawal-reversal] reversal type is PARTIAL");
                    response = processReversalPymd(request, correlationId, request.getAmounts().getTotalAmount()); // REVDEBIT
                    response = processReversalPymd(request, correlationId, request.getAmounts().getDispensedAmount()); // DEBIT
                    break;
                default:
                    log.info("[process-withdrawal-reversal] no specific reversal type, using total amount");
                    response = WithdrawalReversalResponse.createSuccessResponse(request, null, null);
            }
        }
        return response;
    }

    private WithdrawalReversalResponse processReversalPymd(WithdrawalReversalRequest request, String correlationId, BigDecimal amount) {
        try {
            log.info("[process-withdrawal-reversal] calling payment domain reverse service for amount: {}", amount);
            DebitCreditRequest debitCreditRequest = buildDebitCreditRequest(request, "", amount, PYMD_TRANSACTION_REVDEBIT_TYPE);
            DebitCreditResponse pymdResponse = paymentDomainService.postDebitCreditServiceInfo(debitCreditRequest, getOriginalSystem(request), correlationId);

            BigDecimal availableBalance = null;
            BigDecimal ledgerBalance = null;
            if (checkResponseIsNull(pymdResponse)) {
                log.info("[process-withdrawal-reversal] extracting balance information from payment domain response");
                availableBalance = getBalance(pymdResponse, PYMD_ACCOUNT_BALANCE_TYPE_AVAILBALANCE);
                ledgerBalance = getBalance(pymdResponse, PYMD_ACCOUNT_BALANCE_TYPE_BALANCE);
            }
            log.info("[process-withdrawal-reversal] payment domain reverse successful, sending notification");
            NotiRequest notiRequest = buildNotiRequest(request, null, pymdResponse);
            asyncNotificationService.sendNotificationAsync(notiRequest, correlationId);
            return WithdrawalReversalResponse.createSuccessResponseCasePymd(availableBalance, ledgerBalance);
        } catch (RestServiceException e) {
            log.error("[process-reversal-pymd] error occurred: {}", e.getMessage());
            String errorCode = AtmUtil.handleClientError(e);
            if ("JMS01".equals(errorCode) || AtmResponseCodes.AIS_REJECT.equals(errorCode)) {
                return WithdrawalReversalResponse.createErrorResponse(
                    AtmResponseCodes.AIS_REJECT,
                    AtmResponseCodes.getDescription(AtmResponseCodes.AIS_REJECT)
                );
            }
            String responseCode = responseLookupService.getResponseCode(
                request.getProcessingCode().getTransactionType(),
                ResponseFrom.PYMD.getValue(),
                errorCode
            );
            return WithdrawalReversalResponse.createErrorResponse(
                responseCode,
                AtmResponseCodes.getDescription(responseCode)
            );
        } catch (Exception e) {
            return WithdrawalReversalResponse.createErrorResponse(AtmResponseCodes.AIS_REJECT, AtmResponseCodes.getDescription(AtmResponseCodes.AIS_REJECT));
        }
    }

    private boolean checkResponseIsNull(DebitCreditResponse pymdResponse) {
        return pymdResponse.getFromAccount() != null &&
            pymdResponse.getFromAccount().getBalanceInfo() != null &&
            pymdResponse.getFromAccount().getBalanceInfo().getAccountBalances() != null;
    }

    private NotiRequest buildNotiRequest(WithdrawalReversalRequest request,
                                         CoopResponse coopResponse,
                                         DebitCreditResponse debitCreditResponse) {
        String transactionLogKey = duplicateCheckService.generateTransactionLogKey(
                DuplicateCheckMapper.fromWithdrawalReversalRequest(request, request.getOriginalMessageType() != null)
        );
        String  subEventCode = "02";
        String messageType = "RVSL";
        NotiRequest notiRequest = NotiRequest.createNotiRequest(subEventCode,
                transactionLogKey,
                PublicationBody.builder()
                        .messageType(messageType)
                        .cardMaskedNumber(CardUtils.maskCardNumber(request.getCardNumber())) // Mask card number
                        .transactionAmount(request.getAmounts().getTotalAmount())
                        .transactionFee(request.getAmounts().getFeeAmount())
                        .currencyCode("764")
                        .fromAccount(request.getFromAccountNumber())
                        .toAccount(request.getToAccountNumber())
                        .build());
        if (coopResponse != null) {
            // TODO: set coop response to noti request
        } else if (debitCreditResponse != null) {
            BigDecimal availableBalance = null;
            BigDecimal ledgerBalance = null;
            if (checkResponseIsNull(debitCreditResponse)) {
                availableBalance = getBalance(debitCreditResponse, PYMD_ACCOUNT_BALANCE_TYPE_AVAILBALANCE);
                ledgerBalance = getBalance(debitCreditResponse, PYMD_ACCOUNT_BALANCE_TYPE_BALANCE);
            }
            notiRequest.getPublicationBody().setAvailableBalanceSign(CommonUtil.getBalanceSign(availableBalance));
            notiRequest.getPublicationBody().setAvailableBalance(CommonUtil.safeAbs(availableBalance));
            notiRequest.getPublicationBody().setLedgerBalanceSign(CommonUtil.getBalanceSign(ledgerBalance));
            notiRequest.getPublicationBody().setLedgerBalance(CommonUtil.safeAbs(ledgerBalance));
        }
        return notiRequest;
    }

    private BigDecimal getBalance(DebitCreditResponse pymdResponse, String balanceType) {
        return pymdResponse.getFromAccount().getBalanceInfo().getAccountBalances().stream()
            .filter(b -> balanceType.equalsIgnoreCase(b.getBalanceType()))
            .map(b -> b.getAmount())
            .findFirst()
            .orElse(null);
    }

    private DebitCreditRequest buildDebitCreditRequest(WithdrawalReversalRequest request, String originalRequestUid,
                                                       BigDecimal amount, String transactionType) {
        return DebitCreditRequest.builder()
                .bankCode("14")
                .deviceId(request.getTerminalId())
                .financialType("ETFR")
                .originalRequestUid(originalRequestUid)
                .processingBranch(request.getTerminalBranchId())
                .terminalNumber(request.getTerminalId())
                .transactionType(transactionType)
                .fromAccount(AccountRequest.builder()
                        .accountCurrency("764")
                        .accountNumber(request.getFromAccountNumber())
                        .build())
                .remittanceInfo(RemittanceInfo.builder()
                        .amount(amount)
                        .feeChargeAcct("DEPACCTIDFROM")
                        .fees(
                            Arrays.asList(
                                Fees.builder()
                                .feeAmount(request.getAmounts().getFeeAmount())
                                .feeAmountCurrency("764")
                                .feeType("TRAN")
                                .build()
                            )
                        )
                        .build())
                .statementDescription(StatementDescription.builder()
                        .fromDepInfo("SCB".equals(request.getBankCardOwner()) ? request.getTerminalLocation() : request.getTerminalType().concat(request.getBankTerminalOwner()))
                        .build())
                .build();
    }

    private WithdrawalReversalResponse handleDuplicateTransactionForReversal() {
        return WithdrawalReversalResponse.createErrorResponse(
                AtmResponseCodes.INVALID_ACCOUNT,  AtmResponseCodes.MSG_DUPLICATE_TRANSACTION);
    }

    private WithdrawalReversalResponse handleDuplicateCheckErrorForReversal() {
        return WithdrawalReversalResponse.createErrorResponse(
                AtmResponseCodes.FILE_NOT_OPEN,  AtmResponseCodes.getDescription(AtmResponseCodes.FILE_NOT_OPEN));
    }

    private WithdrawalReversalResponse handle71Error() {
        return WithdrawalReversalResponse.createErrorResponse(
                AtmResponseCodes.EXTERNAL_ERROR, AtmResponseCodes.getDescription(AtmResponseCodes.EXTERNAL_ERROR));
    }

    private String getOriginalSystem(WithdrawalReversalRequest request) {
        String bankTerminalOwner = request.getBankTerminalOwner();
        String terminalId = request.getTerminalId();
        if ("SCB".equals(bankTerminalOwner)) {
            if (terminalId != null && terminalId.length() >= 6) {
                char position6 = terminalId.charAt(5);
                if (position6 == '6') {
                    return "SCDM";
                } else {
                    return "SATM";
                }
            } else {
                return "SATM";
            }
        } else if ("MDS".equals(bankTerminalOwner)) {
            return "IATM";
        } else {
            return "OATM";
        }
    }
}
